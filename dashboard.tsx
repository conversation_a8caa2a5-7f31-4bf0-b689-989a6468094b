"use client"

import { useState, use<PERSON>ffect, use<PERSON>em<PERSON>, useRef } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Calendar } from "@/components/ui/calendar"
import { Button } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { CreditCard, DollarSign, Receipt, CalendarIcon, MapPin, TrendingDown, TrendingUp, BarChart3, PieChart, Download } from "lucide-react"
import { format, addDays } from "date-fns"
import { cn } from "@/lib/utils"
import { DateRange } from "react-day-picker"
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ool<PERSON>, ChartTooltipContent } from "@/components/ui/chart"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, LineChart, Line, PieChart as RechartsPC, Pie, Cell, ResponsiveContainer, Legend } from "recharts"
import * as XLSX from 'xlsx'

mapboxgl.accessToken = 'pk.eyJ1IjoidHNhMTciLCJhIjoiY20xcGFtdjlkMDF6bDJtcGJ6bmMxdG43bSJ9.WQ91Sl0anFABQXfqal7A1g';

// Types for our data
type Penjualan = {
  id: string
  total_harga: number
  idmetode_bayar: number
  status_pembayaran: string
  date_created: string
  kangider: { id: string }
  id_detail_penjualan: Array<{
    id: string
    jumlah: number
    harga: number
    idproduk: { id: string; nama_produk: string }
  }>
}

type StockHarian = {
  id: string
  status: string
  stock_awal: number
  stock_akhir: number
  id_kangider: string
  tanggal_stock?: string
  date_created?: string
  id_produk: {
    id: string
    nama_produk: string
    harga: number
  }
}

type LokasiKangider = {
  id: string
  id_kangider: {nama:string}
  latitude: number
  longitude: number
}

type KangIder = {
  id: string
  nama: string
}

type OperationalExpense = {
  id: number
  status: string
  user_created: string
  date_created: string
  expense_name: string
  category: string | null
  amount: number
  keterangan: string
  expense_date: string
  waktu: string
  kangider: string
}

type InternalUsage = {
  id: number
  status: string
  user_created: string
  date_created: string
  quantity: string
  unit_price: string
  total_amount: number
  usage_reason: string
  item_id: number | { id: string; nama_produk: string; harga: number }
  waktu: string
  usage_date: string
  kangider: string
}



const Map = ({ locations }: { locations: LokasiKangider[] }) => {
  const mapContainer = useRef(null);
  const map = useRef<mapboxgl.Map | null>(null);

  useEffect(() => {
    if (map.current) return; // initialize map only once

    try {
      map.current = new mapboxgl.Map({
        container: mapContainer.current!,
        style: 'mapbox://styles/mapbox/streets-v11',
        center: [110.3271942, -7.1297876], // Default center to ungaran,
        zoom: 10,
      });

      // Clear existing markers
      const markers: mapboxgl.Marker[] = [];

      locations.forEach((location) => {
        if (location.latitude && location.longitude && location.id_kangider?.nama) {
          const marker = new mapboxgl.Marker()
            .setLngLat([location.longitude, location.latitude])
            .setPopup(new mapboxgl.Popup().setHTML(`<h3>${location.id_kangider.nama}</h3>`))
            .addTo(map.current!);
          markers.push(marker);
        }
      });


    } catch (error) {
      console.error("Error initializing map:", error);
    }
  }, [locations]);

  if (locations.length === 0) {
    return (
      <div className="flex items-center justify-center h-96 bg-gray-100 rounded-lg">
        <div className="text-center text-gray-500">
          <MapPin className="h-12 w-12 mx-auto mb-2" />
          <p>Tidak ada data lokasi untuk ditampilkan</p>
        </div>
      </div>
    );
  }

  return <div ref={mapContainer} className="map-container" style={{ height: '400px' }} />;
};

export default function Dashboard() {
  // Separate state for selected date range and applied date range
  const [selectedDateRange, setSelectedDateRange] = useState<DateRange>({
    from: addDays(new Date(), -7), // 7 hari yang lalu
    to: new Date() // hari ini
  })
  const [appliedDateRange, setAppliedDateRange] = useState<DateRange>({
    from: addDays(new Date(), -7), // 7 hari yang lalu
    to: new Date() // hari ini
  })
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false)
  const [selectedKangIder, setSelectedKangIder] = useState<string>("all")
  const [selectedPieChartProduct, setSelectedPieChartProduct] = useState<string>("")
  const [selectedAverageKangider, setSelectedAverageKangider] = useState<string>("all")
  const [statisticViewMode, setStatisticViewMode] = useState<"cup" | "value">("cup")
  const [penjualan, setPenjualan] = useState<Penjualan[]>([])
  const [stockHarian, setStockHarian] = useState<StockHarian[]>([])
  const [lokasiKangider, setLokasiKangider] = useState<LokasiKangider[]>([])
  const [kangIders, setKangIders] = useState<KangIder[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const [email, setEmail] = useState<string>("")
  const [password, setPassword] = useState<string>("")
  const [filteredPenjualan, setFilteredPenjualan] = useState<Penjualan[]>([])
  const [filteredStockHarian, setFilteredStockHarian] = useState<StockHarian[]>([])
  const [operationalExpenses, setOperationalExpenses] = useState<OperationalExpense[]>([])
  const [internalUsages, setInternalUsages] = useState<InternalUsage[]>([])
  const [filteredOperationalExpenses, setFilteredOperationalExpenses] = useState<OperationalExpense[]>([])
  const [filteredInternalUsages, setFilteredInternalUsages] = useState<InternalUsage[]>([])


  const handleLogin = async () => {
    try {
      setError(null) // Clear previous errors


      const res = await fetch("https://api.iderkopi.id/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      })

      if (!res.ok) {
        setError(`Login failed: ${res.status} ${res.statusText}`)
        return
      }

      const data = await res.json()

      if (data.data && data.data.access_token) {
        setToken(data.data.access_token)
      } else {
        setError("Login failed. No access token received.")
      }
    } catch (err) {
      setError(`Login failed: ${err instanceof Error ? err.message : 'Unknown error'}`)
    }
  }

  const exportToExcel = () => {
    try {
      // Prepare data for export
      const workbook = XLSX.utils.book_new()

      // 1. Summary Sheet
      const summaryData = [
        ['LAPORAN DASHBOARD KANG IDER'],
        [''],
        ['Periode:', `${format(appliedDateRange.from!, "dd/MM/yyyy")} - ${format(appliedDateRange.to || appliedDateRange.from!, "dd/MM/yyyy")}`],
        ['Kang Ider:', selectedKangIder === "all" ? "Semua" : kangIders.find(k => k.id === selectedKangIder)?.nama || "Tidak diketahui"],
        ['Tanggal Export:', format(new Date(), "dd/MM/yyyy HH:mm:ss")],
        [''],
        ['RINGKASAN KEUANGAN'],
        ['Total Pembayaran Cash', `Rp ${totals.totalCash.toLocaleString()}`],
        ['Total Pembayaran QRIS', `Rp ${totals.totalQRIS.toLocaleString()}`],
        ['Total Pendapatan', `Rp ${totals.totalPendapatan.toLocaleString()}`],
        ['Total Pengeluaran Operasional', `Rp ${totals.totalOperationalExpenses.toLocaleString()}`],
        ['Total Penggunaan Internal', `Rp ${totals.totalInternalUsage.toLocaleString()}`],
        ['Total Pengeluaran', `Rp ${totals.totalPengeluaran.toLocaleString()}`],
        ['Omset Bersih', `Rp ${totals.omsetBersih.toLocaleString()}`],
      ]

      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData)
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Ringkasan')

      // 2. Penjualan Sheet
      const penjualanData = [
        ['DATA PENJUALAN'],
        [''],
        ['ID', 'Tanggal', 'Total Harga', 'Metode Bayar', 'Status', 'Kang Ider']
      ]

      filteredPenjualan.forEach(item => {
        penjualanData.push([
          item.id,
          format(new Date(item.date_created), "dd/MM/yyyy HH:mm"),
          item.total_harga,
          item.idmetode_bayar === 1 ? 'Cash' : 'QRIS',
          item.status_pembayaran,
          kangIders.find(k => k.id === item.kangider?.id)?.nama || item.kangider?.id || ''
        ] as any)
      })

      const penjualanSheet = XLSX.utils.aoa_to_sheet(penjualanData)
      XLSX.utils.book_append_sheet(workbook, penjualanSheet, 'Penjualan')

      // 3. Menu dan Stock Sheet
      const menuData = [
        ['DATA MENU DAN STOCK'],
        [''],
        ['Menu', 'Harga', 'Stock Awal', 'Terjual', 'Stock Akhir', 'Nilai Penjualan', 'Status Stock']
      ]

      Object.keys(groupedData.groupedStock).forEach(idProduk => {
        const stockItem = groupedData.groupedStock[idProduk]
        const penjualanItem = groupedData.groupedPenjualan[idProduk] || { total_terjual: 0 }
        const hargaProduk = stockItem.harga_produk || 0
        const nilaiPenjualan = hargaProduk * penjualanItem.total_terjual
        const stockTerjualPersen = stockItem.stock_awal > 0 ? (penjualanItem.total_terjual / stockItem.stock_awal) * 100 : 0
        const stockStatus = stockItem.stock_akhir === 0 ? 'Habis' :
                           stockItem.stock_akhir <= stockItem.stock_awal * 0.2 ? 'Sedikit' : 'Cukup'

        menuData.push([
          stockItem.nama_produk,
          hargaProduk,
          stockItem.stock_awal,
          penjualanItem.total_terjual,
          stockItem.stock_akhir,
          nilaiPenjualan,
          `${stockStatus} (${stockTerjualPersen.toFixed(0)}% terjual)`
        ] as any)
      })

      const menuSheet = XLSX.utils.aoa_to_sheet(menuData)
      XLSX.utils.book_append_sheet(workbook, menuSheet, 'Menu & Stock')

      // 4. Pengeluaran Operasional Sheet
      if (filteredOperationalExpenses.length > 0) {
        const expenseData = [
          ['PENGELUARAN OPERASIONAL'],
          [''],
          ['ID', 'Tanggal', 'Waktu', 'Nama Pengeluaran', 'Kategori', 'Jumlah', 'Keterangan', 'Kang Ider']
        ]

        filteredOperationalExpenses.forEach(expense => {
          expenseData.push([
            expense.id,
            expense.expense_date,
            expense.waktu,
            expense.expense_name,
            expense.category || '',
            expense.amount,
            expense.keterangan,
            kangIders.find(k => k.id === expense.kangider)?.nama || expense.kangider
          ] as any)
        })

        const expenseSheet = XLSX.utils.aoa_to_sheet(expenseData)
        XLSX.utils.book_append_sheet(workbook, expenseSheet, 'Pengeluaran Operasional')
      }

      // 5. Penggunaan Internal Sheet
      if (filteredInternalUsages.length > 0) {
        const usageData = [
          ['PENGGUNAAN INTERNAL'],
          [''],
          ['ID', 'Tanggal', 'Waktu', 'Nama Produk', 'Quantity', 'Harga Satuan', 'Total Amount', 'Alasan', 'Kang Ider']
        ]

        filteredInternalUsages.forEach(usage => {
          // Handle usage_date properly - use date_created as fallback if usage_date is invalid
          const displayDate = usage.usage_date && usage.usage_date !== "0"
            ? usage.usage_date
            : usage.date_created
              ? format(new Date(usage.date_created), "yyyy-MM-dd")
              : "Tanggal tidak tersedia"

          // Get product name using helper function
          const productName = getProductName(usage)

          usageData.push([
            usage.id,
            displayDate,
            usage.waktu,
            productName,
            usage.quantity,
            parseInt(usage.unit_price),
            usage.total_amount,
            usage.usage_reason,
            kangIders.find(k => k.id === usage.kangider)?.nama || usage.kangider
          ] as any)
        })

        const usageSheet = XLSX.utils.aoa_to_sheet(usageData)
        XLSX.utils.book_append_sheet(workbook, usageSheet, 'Penggunaan Internal')
      }

      // Generate filename
      const filename = `Laporan_Dashboard_${format(appliedDateRange.from!, "yyyy-MM-dd")}_${format(appliedDateRange.to || appliedDateRange.from!, "yyyy-MM-dd")}.xlsx`

      // Save file
      XLSX.writeFile(workbook, filename)

    } catch (error) {
      console.error('Error exporting to Excel:', error)
      alert('Terjadi kesalahan saat export Excel. Silakan coba lagi.')
    }
  }

  useEffect(() => {
    if (!token) return

    const fetchData = async () => {
      setIsLoading(true)
      setError(null)
      try {
        // Gunakan rentang tanggal yang sudah diapply
        const fromDateString = format(appliedDateRange.from!, "yyyy-MM-dd")
        const toDateString = appliedDateRange.to ? format(appliedDateRange.to, "yyyy-MM-dd") : fromDateString

        const headers = {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        }

        // Fetch penjualan data dengan rentang tanggal
        const penjualanUrl = `https://api.iderkopi.id/items/penjualan?fields=id,user_created,date_created,total_harga,idmetode_bayar,status_pembayaran,kangider.id,id_detail_penjualan.id,id_detail_penjualan.jumlah,id_detail_penjualan.harga,id_detail_penjualan.idproduk,id_detail_penjualan.idproduk.nama_produk,id_detail_penjualan.idproduk.id&filter[tanggal_transaksi][_between]=${fromDateString}T00:00:00,${toDateString}T23:59:59&limit=-1`
        const penjualanRes = await fetch(penjualanUrl, { headers })

        if (!penjualanRes.ok) {
          if (penjualanRes.status === 403) {
            throw new Error(`Access denied (403): User tidak memiliki permission untuk mengakses data penjualan. Hubungi administrator untuk memberikan akses.`)
          } else if (penjualanRes.status === 401) {
            throw new Error(`Unauthorized (401): Token expired atau invalid. Silakan login ulang.`)
          } else {
            throw new Error(`Penjualan API Error: ${penjualanRes.status} ${penjualanRes.statusText}`)
          }
        }

        const penjualanData = await penjualanRes.json()
        setPenjualan(penjualanData.data || [])

        // Fetch stock data dengan rentang tanggal
        const stockUrl = `https://api.iderkopi.id/items/stock_harian?fields=id,status,tanggal_stock,stock_awal,stock_akhir,id_kangider,id_produk.id,id_produk.nama_produk,id_produk.foto,id_produk.harga&filter[tanggal_stock][_between]=${fromDateString},${toDateString}&limit=-1`
        const stockRes = await fetch(stockUrl, { headers })

        if (!stockRes.ok) {
          if (stockRes.status === 403) {
            throw new Error(`Access denied (403): User tidak memiliki permission untuk mengakses data stock. Hubungi administrator untuk memberikan akses.`)
          } else if (stockRes.status === 401) {
            throw new Error(`Unauthorized (401): Token expired atau invalid. Silakan login ulang.`)
          } else {
            throw new Error(`Stock API Error: ${stockRes.status} ${stockRes.statusText}`)
          }
        }

        const stockData = await stockRes.json()
        setStockHarian(stockData.data || [])

        // Fetch lokasi data dengan rentang tanggal
        const lokasiUrl = `https://api.iderkopi.id/items/lokasi_kangider?fields=id,date_created,id_kangider.nama,latitude,longitude&filter[date_created][_between]=${fromDateString},${toDateString}`
        const lokasiRes = await fetch(lokasiUrl, { headers })

        if (!lokasiRes.ok) {
          if (lokasiRes.status === 403) {
            throw new Error(`Access denied (403): User tidak memiliki permission untuk mengakses data lokasi. Hubungi administrator untuk memberikan akses.`)
          } else if (lokasiRes.status === 401) {
            throw new Error(`Unauthorized (401): Token expired atau invalid. Silakan login ulang.`)
          } else {
            throw new Error(`Lokasi API Error: ${lokasiRes.status} ${lokasiRes.statusText}`)
          }
        }

        const lokasiData = await lokasiRes.json()
        setLokasiKangider(lokasiData.data || [])

        // Fetch kangider data
        const kangiderRes = await fetch("https://api.iderkopi.id/items/kangider", { headers })

        if (!kangiderRes.ok) {
          if (kangiderRes.status === 403) {
            throw new Error(`Access denied (403): User tidak memiliki permission untuk mengakses data kangider. Hubungi administrator untuk memberikan akses.`)
          } else if (kangiderRes.status === 401) {
            throw new Error(`Unauthorized (401): Token expired atau invalid. Silakan login ulang.`)
          } else {
            throw new Error(`Kangider API Error: ${kangiderRes.status} ${kangiderRes.statusText}`)
          }
        }

        const kangiderData = await kangiderRes.json()
        if (Array.isArray(kangiderData.data)) {
          setKangIders(kangiderData.data.map((k: any) => ({ id: k.id, nama: k.nama })))
        } else {
          setKangIders([])
        }

        // Fetch operational expenses data dengan rentang tanggal
        const expensesUrl = `https://api.iderkopi.id/items/operational_expenses?fields=id,status,user_created,date_created,expense_name,category,amount,keterangan,expense_date,waktu,kangider&filter[expense_date][_between]=${fromDateString},${toDateString}&limit=-1`
        const expensesRes = await fetch(expensesUrl, { headers })

        if (!expensesRes.ok) {
          console.warn(`Operational Expenses API Warning: ${expensesRes.status} ${expensesRes.statusText}`)
          setOperationalExpenses([])
        } else {
          const expensesData = await expensesRes.json()
          setOperationalExpenses(expensesData.data || [])
        }

        // Fetch internal usage data dengan rentang tanggal - include product details
        const usageUrl = `https://api.iderkopi.id/items/internal_usages?fields=id,status,user_created,date_created,quantity,unit_price,total_amount,usage_reason,item_id,item_id.id,item_id.nama_produk,item_id.harga,waktu,usage_date,kangider&filter[usage_date][_between]=${fromDateString},${toDateString}&limit=-1`
        const usageRes = await fetch(usageUrl, { headers })

        if (!usageRes.ok) {
          console.warn(`Internal Usage API Warning: ${usageRes.status} ${usageRes.statusText}`)
          setInternalUsages([])
        } else {
          const usageData = await usageRes.json()
          setInternalUsages(usageData.data || [])
        }
      } catch (err) {
        let errorMessage = 'Unknown error'
        if (err instanceof Error) {
          errorMessage = err.message
        }
        setError(`Terjadi kesalahan saat mengambil data: ${errorMessage}`)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [appliedDateRange, token])

  useEffect(() => {
    const filteredPenjualan =
      selectedKangIder === "all" ? penjualan : penjualan.filter((p) => p.kangider?.id === selectedKangIder)
    setFilteredPenjualan(filteredPenjualan)

    const filteredStockHarian =
      selectedKangIder === "all" ? stockHarian : stockHarian.filter((s) => s.id_kangider === selectedKangIder)
    setFilteredStockHarian(filteredStockHarian)

    const filteredExpenses =
      selectedKangIder === "all" ? operationalExpenses : operationalExpenses.filter((e) => e.kangider === selectedKangIder)
    setFilteredOperationalExpenses(filteredExpenses)

    const filteredUsages =
      selectedKangIder === "all" ? internalUsages : internalUsages.filter((u) => u.kangider === selectedKangIder)
    setFilteredInternalUsages(filteredUsages)
  }, [penjualan, stockHarian, operationalExpenses, internalUsages, selectedKangIder])



  const groupedData = useMemo(() => {
    const groupedPenjualan = filteredPenjualan.reduce((acc, item) => {
      item.id_detail_penjualan.forEach((detail) => {
        const idProduk = detail.idproduk.id
        if (!acc[idProduk]) {
          acc[idProduk] = {
            nama_produk: detail.idproduk.nama_produk,
            total_terjual: 0,
            total_harga: 0,
            harga_produk: detail.harga, // Tambahkan harga produk
          }
        }
        acc[idProduk].total_terjual += detail.jumlah
        acc[idProduk].total_harga += detail.harga * detail.jumlah
      })
      return acc
    }, {} as Record<string, { nama_produk: string; total_terjual: number; total_harga: number; harga_produk: number }>)

    const groupedStock = filteredStockHarian.reduce((acc, item) => {
      const idProduk = item.id_produk.id
      if (!acc[idProduk]) {
        acc[idProduk] = {
          nama_produk: item.id_produk.nama_produk,
          stock_awal: 0,
          stock_akhir: 0,
          harga_produk: item.id_produk.harga, // Tambahkan harga produk
        }
      }
      acc[idProduk].stock_awal += item.stock_awal
      acc[idProduk].stock_akhir += item.stock_akhir
      return acc
    }, {} as Record<string, { nama_produk: string; stock_awal: number; stock_akhir: number; harga_produk: number }>)

    return { groupedPenjualan, groupedStock }
  }, [filteredPenjualan, filteredStockHarian])

  // Data untuk pie chart penjualan per produk (menampilkan distribusi kangider per produk)
  const pieChartData = useMemo(() => {
    const productKangiderData: Record<string, Record<string, number>> = {}

    filteredPenjualan.forEach(penjualan => {
      const kangiderNama = kangIders.find(k => k.id === penjualan.kangider?.id)?.nama || 'Unknown'

      penjualan.id_detail_penjualan.forEach(detail => {
        const productName = detail.idproduk.nama_produk

        if (!productKangiderData[productName]) {
          productKangiderData[productName] = {}
        }

        if (!productKangiderData[productName][kangiderNama]) {
          productKangiderData[productName][kangiderNama] = 0
        }

        productKangiderData[productName][kangiderNama] += detail.jumlah
      })
    })

    return productKangiderData
  }, [filteredPenjualan, kangIders])

  // Daftar produk unik untuk dropdown
  const availableProducts = useMemo(() => {
    const productsMap: Record<string, {id: string, name: string}> = {}

    filteredPenjualan.forEach(penjualan => {
      penjualan.id_detail_penjualan.forEach(detail => {
        const productId = detail.idproduk.id
        const productName = detail.idproduk.nama_produk

        if (!productsMap[productId]) {
          productsMap[productId] = {
            id: productId,
            name: productName
          }
        }
      })
    })

    return Object.values(productsMap).sort((a, b) => a.name.localeCompare(b.name))
  }, [filteredPenjualan])

  // Set default produk untuk pie chart
  useEffect(() => {
    if (availableProducts.length > 0 && !selectedPieChartProduct) {
      setSelectedPieChartProduct(availableProducts[0].id)
    }
  }, [availableProducts, selectedPieChartProduct])

  // Helper function to get product name from item_id
  const getProductName = (usage: InternalUsage): string => {
    // If item_id is an object with product details, use nama_produk
    if (typeof usage.item_id === 'object' && usage.item_id?.nama_produk) {
      return usage.item_id.nama_produk
    }

    // Fallback: try to find product name from stock data
    const itemId = typeof usage.item_id === 'object' ? usage.item_id.id : usage.item_id.toString()
    const stockItem = filteredStockHarian.find(stock => stock.id_produk.id === itemId)
    if (stockItem) {
      return stockItem.id_produk.nama_produk
    }

    // Final fallback: show item ID
    return `Item ID: ${itemId}`
  }

  const totals = useMemo(() => {
    let totalPenjualan = 0;
    let totalCash = 0;
    let totalQRIS = 0;

    filteredPenjualan.forEach(item => {
      const transactionTotal = item.total_harga;

      if (item.idmetode_bayar === 1) {
        totalCash += transactionTotal;
      } else if (item.idmetode_bayar === 2) {
        totalQRIS += transactionTotal;
      }

      totalPenjualan += transactionTotal;
    });

    // Hitung total operational expenses
    let totalOperationalExpenses = 0;
    filteredOperationalExpenses.forEach(expense => {
      totalOperationalExpenses += expense.amount;
    });

    // Hitung total internal usage
    let totalInternalUsage = 0;
    filteredInternalUsages.forEach(usage => {
      totalInternalUsage += usage.total_amount;
    });

    // Hitung total pengeluaran dan omset bersih
    const totalPengeluaran = totalOperationalExpenses + totalInternalUsage;
    const omsetBersih = totalPenjualan - totalPengeluaran;

    return {
      totalCash,
      totalQRIS,
      totalPendapatan: totalPenjualan,
      totalOperationalExpenses,
      totalInternalUsage,
      totalPengeluaran,
      omsetBersih
    };
  }, [filteredPenjualan, filteredOperationalExpenses, filteredInternalUsages]);

  if (!token) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md">
          <h2 className="text-2xl font-bold text-center">Login</h2>

          {/* Info tentang permission */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm">
            <div className="font-medium text-blue-800 mb-1">ℹ️ Informasi:</div>
            <div className="text-blue-700">
              Dashboard ini memerlukan akun dengan permission khusus untuk mengakses data penjualan, stock, dan lokasi.
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full p-2 mt-1 border rounded-md"
              placeholder="Masukkan email Anda"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Password</label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full p-2 mt-1 border rounded-md"
              placeholder="Masukkan password Anda"
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleLogin();
                }
              }}
            />
          </div>
          <Button onClick={handleLogin} className="w-full mt-4">
            Login
          </Button>
          {error && (
            <div className="text-red-500 mt-2 p-2 bg-red-50 border border-red-200 rounded">
              {error}
            </div>
          )}


        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4 text-lg">Memuat data dashboard...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center max-w-lg">
          <div className="text-red-500 text-xl mb-4">⚠️ Terjadi Kesalahan</div>
          <div className="text-red-600 mb-4">{error}</div>

          {/* Tampilkan solusi khusus untuk error "Body is disturbed or locked" */}
          {error.includes('Body is disturbed or locked') && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4 text-left">
              <div className="font-medium text-yellow-800 mb-2">💡 Solusi Cepat:</div>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Refresh halaman (F5 atau Ctrl+R)</li>
                <li>• Clear browser cache</li>
                <li>• Gunakan mode demo untuk testing</li>
              </ul>
            </div>
          )}

          {/* Tampilkan solusi khusus untuk error 403 (Access Denied) */}
          {error.includes('403') && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4 text-left">
              <div className="font-medium text-red-800 mb-2">🚫 Access Denied (403):</div>
              <ul className="text-sm text-red-700 space-y-1">
                <li>• User tidak memiliki permission untuk mengakses data</li>
                <li>• Hubungi administrator untuk memberikan akses</li>
                <li>• Pastikan akun memiliki role yang sesuai</li>
                <li>• Coba login dengan akun yang berbeda</li>
              </ul>
            </div>
          )}

          {/* Tampilkan solusi khusus untuk error 401 (Unauthorized) */}
          {error.includes('401') && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4 text-left">
              <div className="font-medium text-orange-800 mb-2">🔐 Unauthorized (401):</div>
              <ul className="text-sm text-orange-700 space-y-1">
                <li>• Token expired atau invalid</li>
                <li>• Silakan login ulang</li>
                <li>• Pastikan credentials benar</li>
              </ul>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-2 justify-center">
            {/* Tombol khusus untuk error 401/403 */}
            {(error.includes('401') || error.includes('403')) && (
              <Button
                onClick={() => {
                  setToken(null);
                  setError(null);
                  setEmail("");
                  setPassword("");
                }}
                className="mt-4"
              >
                🔐 Login Ulang
              </Button>
            )}

            <Button onClick={() => window.location.reload()} className="mt-4">
              🔄 Muat Ulang Halaman
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                // Clear localStorage dan sessionStorage
                localStorage.clear();
                sessionStorage.clear();
                window.location.reload();
              }}
              className="mt-4"
            >
              🗑️ Clear Cache & Reload
            </Button>

          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col bg-gray-50 p-6">


      {/* Header Section */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Ringkasan Penjualan</h1>

        {/* Filter Controls */}
        <div className="bg-white rounded-lg border p-4 shadow-sm">
          <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
            <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
              {/* Period Info */}
              <div className="text-sm text-gray-600 bg-gray-50 px-3 py-2 rounded-md border">
                📅 Periode Aktif: <span className="font-medium text-gray-900">
                  {appliedDateRange.from ? format(appliedDateRange.from, "dd MMM yyyy") : "Tidak dipilih"}
                  {appliedDateRange.to && appliedDateRange.from !== appliedDateRange.to ? ` - ${format(appliedDateRange.to, "dd MMM yyyy")}` : ""}
                </span>
              </div>
          {/* Date Range Picker dengan style yang lebih kompak */}
          <div className="flex items-center gap-2">
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={cn("w-[280px] justify-start text-left font-normal text-sm", !selectedDateRange && "text-muted-foreground")}
                >
                  {selectedDateRange.from ? (
                    selectedDateRange.to ? (
                      <>
                        {format(selectedDateRange.from, "dd MMM yyyy")} - {format(selectedDateRange.to, "dd MMM yyyy")}
                      </>
                    ) : (
                      format(selectedDateRange.from, "dd MMM yyyy")
                    )
                  ) : (
                    <span>Pilih rentang tanggal</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <div className="p-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-sm">Pilih Periode</h4>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const today = new Date();
                            setSelectedDateRange({
                              from: today,
                              to: today
                            });
                          }}
                          className="text-xs h-7 px-2"
                        >
                          Hari Ini
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const today = new Date();
                            const weekAgo = addDays(today, -7);
                            setSelectedDateRange({
                              from: weekAgo,
                              to: today
                            });
                          }}
                          className="text-xs h-7 px-2"
                        >
                          7 Hari
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const today = new Date();
                            const monthAgo = addDays(today, -30);
                            setSelectedDateRange({
                              from: monthAgo,
                              to: today
                            });
                          }}
                          className="text-xs h-7 px-2"
                        >
                          30 Hari
                        </Button>
                      </div>
                    </div>
                    <Calendar
                      mode="range"
                      selected={selectedDateRange}
                      onSelect={(range) => {
                        if (range) setSelectedDateRange(range);
                      }}
                      initialFocus
                      numberOfMonths={2}
                      className="rounded-md border-0"
                      classNames={{
                        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
                        month: "space-y-4",
                        caption: "flex justify-center pt-1 relative items-center",
                        caption_label: "text-sm font-medium",
                        nav: "space-x-1 flex items-center",
                        nav_button: "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
                        nav_button_previous: "absolute left-1",
                        nav_button_next: "absolute right-1",
                        table: "w-full border-collapse space-y-1",
                        head_row: "flex",
                        head_cell: "text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",
                        row: "flex w-full mt-2",
                        cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                        day: "h-8 w-8 p-0 font-normal aria-selected:opacity-100",
                        day_selected: "bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",
                        day_today: "bg-accent text-accent-foreground",
                        day_outside: "text-muted-foreground opacity-50",
                        day_disabled: "text-muted-foreground opacity-50",
                        day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
                        day_hidden: "invisible",
                      }}
                    />

                    {/* Action Buttons */}
                    <div className="flex justify-end gap-2 pt-4 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedDateRange({
                            from: addDays(new Date(), -7),
                            to: new Date()
                          });
                        }}
                        className="h-8"
                      >
                        Reset
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => {
                          // Apply the selected date range
                          setAppliedDateRange(selectedDateRange);
                          // Close the popover
                          setIsDatePickerOpen(false);
                        }}
                        className="h-8 bg-green-600 hover:bg-green-700"
                      >
                        Proses
                      </Button>
                    </div>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          </div>

          {/* Kang Ider Filter */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Kang Ider:</span>
            <Select value={selectedKangIder} onValueChange={setSelectedKangIder}>
              <SelectTrigger className="w-[180px] h-9">
                <SelectValue placeholder="Pilih Kang Ider" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Kang Ider</SelectItem>
                {kangIders.map((kangIder) => (
                  <SelectItem key={kangIder.id} value={kangIder.id}>
                    {kangIder.nama}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={exportToExcel}
              className="h-9 bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
            >
              <Download className="h-4 w-4 mr-2" />
              Export Excel
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const today = new Date();
                const monthAgo = addDays(today, -30);
                setSelectedDateRange({
                  from: monthAgo,
                  to: today
                });
                setAppliedDateRange({
                  from: monthAgo,
                  to: today
                });
              }}
              className="h-9"
            >
              Reset Filter
            </Button>
            <Button
              size="sm"
              onClick={() => {
                // Trigger refresh data
                window.location.reload();
              }}
              className="h-9"
            >
              Refresh Data
            </Button>
          </div>
            </div>

            {/* Summary Info */}
            <div className="text-right">
              <div className="text-sm text-muted-foreground">
                Total Transaksi: <span className="font-medium text-foreground">{filteredPenjualan.length}</span>
              </div>
              <div className="text-sm text-muted-foreground">
                Periode: <span className="font-medium text-foreground">
                  {appliedDateRange.from && appliedDateRange.to ?
                    `${Math.ceil((appliedDateRange.to.getTime() - appliedDateRange.from.getTime()) / (1000 * 60 * 60 * 24)) + 1} hari` :
                    "1 hari"
                  }
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tampilkan pesan jika tidak ada data */}
      {filteredPenjualan.length === 0 && filteredStockHarian.length === 0 && filteredOperationalExpenses.length === 0 && filteredInternalUsages.length === 0 && (
        <div className="mt-8 p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="text-center">
            <div className="text-yellow-600 text-lg font-medium mb-2">
              📊 Tidak Ada Data Ditemukan
            </div>
            <p className="text-yellow-700 mb-4">
              Tidak ada data penjualan, stock, atau pengeluaran untuk rentang tanggal dan Kang Ider yang dipilih.
            </p>
            <div className="text-sm text-yellow-600">
              <p>Rentang tanggal: {format(appliedDateRange.from!, "dd/MM/yyyy")} - {format(appliedDateRange.to || appliedDateRange.from!, "dd/MM/yyyy")}</p>
              <p>Kang Ider: {selectedKangIder === "all" ? "Semua" : kangIders.find(k => k.id === selectedKangIder)?.nama || "Tidak diketahui"}</p>
            </div>
          </div>
        </div>
      )}

      <div className="mt-6 grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
        <Card className="bg-white shadow-sm border-0">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Pembayaran Cash</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">Rp {totals.totalCash.toLocaleString()}</div>
            <p className="text-xs text-gray-500 mt-1">
              {filteredPenjualan.filter(p => p.idmetode_bayar === 1).length} transaksi
            </p>
          </CardContent>
        </Card>
        <Card className="bg-white shadow-sm border-0">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Pembayaran QRIS</CardTitle>
            <CreditCard className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">Rp {totals.totalQRIS.toLocaleString()}</div>
            <p className="text-xs text-gray-500 mt-1">
              {filteredPenjualan.filter(p => p.idmetode_bayar === 2).length} transaksi
            </p>
          </CardContent>
        </Card>
        <Card className="bg-white shadow-sm border-0">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Pendapatan</CardTitle>
            <Receipt className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">Rp {totals.totalPendapatan.toLocaleString()}</div>
            <p className="text-xs text-gray-500 mt-1">
              {filteredPenjualan.length} total transaksi
            </p>
          </CardContent>
        </Card>
        <Card className="bg-white shadow-sm border-0">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Pengeluaran</CardTitle>
            <TrendingDown className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">Rp {totals.totalPengeluaran.toLocaleString()}</div>
            <p className="text-xs text-gray-500 mt-1">
              {filteredOperationalExpenses.length + filteredInternalUsages.length} total pengeluaran
            </p>
          </CardContent>
        </Card>
        <Card className="bg-white shadow-sm border-0">
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Omset Bersih</CardTitle>
            <TrendingUp className={`h-4 w-4 ${totals.omsetBersih >= 0 ? 'text-green-600' : 'text-red-600'}`} />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${totals.omsetBersih >= 0 ? 'text-green-900' : 'text-red-900'}`}>
              Rp {totals.omsetBersih.toLocaleString()}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Pendapatan - Pengeluaran
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="mt-8">
        <div className="mb-4">
          <h2 className="text-xl font-bold text-gray-900">📊 Analisis dan Grafik</h2>
          <p className="text-sm text-gray-600">Visualisasi data penjualan dan performa kangider</p>
        </div>
        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
        {/* Grafik Produk Terlaris */}
        <Card className="bg-white shadow-sm border-0">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              Produk Terlaris (Top 10)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                terjual: {
                  label: "Terjual",
                  color: "hsl(var(--chart-1))",
                },
              }}
              className="h-[300px]"
            >
              <BarChart
                data={Object.keys(groupedData.groupedStock)
                  .map((idProduk) => {
                    const stockItem = groupedData.groupedStock[idProduk]
                    const penjualanItem = groupedData.groupedPenjualan[idProduk] || { total_terjual: 0 }
                    return {
                      nama: stockItem.nama_produk,
                      terjual: penjualanItem.total_terjual,
                      nilai: (stockItem.harga_produk || 0) * penjualanItem.total_terjual
                    }
                  })
                  .sort((a, b) => b.terjual - a.terjual)
                  .slice(0, 10)}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="nama"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                />
                <YAxis />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value, name) => [
                    `${value} cup`,
                    name === 'terjual' ? 'Terjual' : name
                  ]}
                />
                <Bar dataKey="terjual" fill="#3b82f6" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Grafik Capaian Omset per Kangider */}
        <Card className="bg-white shadow-sm border-0">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <PieChart className="h-5 w-5 text-green-600" />
              Capaian Omset per Kangider
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                omset: {
                  label: "Omset Bersih",
                  color: "hsl(var(--chart-2))",
                },
              }}
              className="h-[300px]"
            >
              <BarChart
                data={kangIders.map((kangider) => {
                  // Filter data per kangider
                  const kangiderPenjualan = penjualan.filter(p => p.kangider?.id === kangider.id)
                  const kangiderExpenses = operationalExpenses.filter(e => e.kangider === kangider.id)
                  const kangiderUsages = internalUsages.filter(u => u.kangider === kangider.id)

                  const totalPendapatan = kangiderPenjualan.reduce((sum, p) => sum + p.total_harga, 0)
                  const totalPengeluaran = kangiderExpenses.reduce((sum, e) => sum + e.amount, 0) +
                                          kangiderUsages.reduce((sum, u) => sum + u.total_amount, 0)
                  const omsetBersih = totalPendapatan - totalPengeluaran

                  return {
                    nama: kangider.nama,
                    pendapatan: totalPendapatan,
                    pengeluaran: totalPengeluaran,
                    omset: omsetBersih
                  }
                }).filter(item => item.pendapatan > 0 || item.pengeluaran > 0)}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="nama"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                />
                <YAxis />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value, name) => [
                    `Rp ${value.toLocaleString()}`,
                    name === 'omset' ? 'Omset Bersih' :
                    name === 'pendapatan' ? 'Pendapatan' : 'Pengeluaran'
                  ]}
                />
                <Bar dataKey="pendapatan" fill="#10b981" radius={[4, 4, 0, 0]} />
                <Bar dataKey="pengeluaran" fill="#ef4444" radius={[4, 4, 0, 0]} />
                <Bar dataKey="omset" fill="#3b82f6" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>
        </div>
      </div>

      {/* Additional Charts Section */}
      <div className="mt-8">
        <div className="mb-4">
          <h2 className="text-xl font-bold text-gray-900">📈 Analisis Lanjutan</h2>
          <p className="text-sm text-gray-600">Stock management dan trend penjualan</p>
        </div>
        <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
        {/* Grafik Stock Harian per Kangider */}
        <Card className="bg-white shadow-sm border-0">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-purple-600" />
              Stock Harian per Kangider
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                ...kangIders.reduce((acc, kangider, index) => {
                  acc[kangider.id] = {
                    label: kangider.nama,
                    color: `hsl(${(index * 60) % 360}, 70%, 50%)`,
                  }
                  return acc
                }, {} as any)
              }}
              className="h-[300px]"
            >
              <BarChart
                data={(() => {
                  // Group stock by date
                  const stockByDate = filteredStockHarian.reduce((acc, stock) => {
                    const date = stock.tanggal_stock || (stock.date_created ? new Date(stock.date_created).toISOString().split('T')[0] : new Date().toISOString().split('T')[0])
                    const dateKey = new Date(date).toLocaleDateString('id-ID', {
                      day: '2-digit',
                      month: '2-digit'
                    })

                    if (!acc[dateKey]) {
                      acc[dateKey] = { tanggal: dateKey }
                    }

                    // Sum stock awal per kangider per date
                    const kangiderName = kangIders.find(k => k.id === stock.id_kangider)?.nama || stock.id_kangider
                    if (!acc[dateKey][kangiderName]) {
                      acc[dateKey][kangiderName] = 0
                    }
                    acc[dateKey][kangiderName] += stock.stock_awal

                    return acc
                  }, {} as Record<string, any>)

                  return Object.values(stockByDate)
                    .sort((a, b) => new Date(a.tanggal).getTime() - new Date(b.tanggal).getTime())
                    .slice(-7) // Show last 7 days
                })()}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="tanggal"
                  fontSize={12}
                />
                <YAxis />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value, name) => [
                    `${value} cup`,
                    `Stock ${name}`
                  ]}
                />
                {kangIders.map((kangider, index) => (
                  <Bar
                    key={kangider.id}
                    dataKey={kangider.nama}
                    fill={`hsl(${(index * 60) % 360}, 70%, 50%)`}
                    radius={[2, 2, 0, 0]}
                  />
                ))}
              </BarChart>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Grafik Trend Penjualan */}
        <Card className="bg-white shadow-sm border-0">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-orange-600" />
              Trend Penjualan Harian
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                penjualan: {
                  label: "Penjualan",
                  color: "hsl(var(--chart-5))",
                },
              }}
              className="h-[300px]"
            >
              <LineChart
                data={(() => {
                  // Group penjualan by date
                  const salesByDate = filteredPenjualan.reduce((acc, sale) => {
                    const date = new Date(sale.date_created).toISOString().split('T')[0]
                    if (!acc[date]) {
                      acc[date] = { total: 0, count: 0 }
                    }
                    acc[date].total += sale.total_harga
                    acc[date].count += 1
                    return acc
                  }, {} as Record<string, { total: number; count: number }>)

                  return Object.entries(salesByDate)
                    .map(([date, data]) => ({
                      tanggal: new Date(date).toLocaleDateString('id-ID', {
                        day: '2-digit',
                        month: '2-digit'
                      }),
                      penjualan: data.total,
                      transaksi: data.count
                    }))
                    .sort((a, b) => new Date(a.tanggal).getTime() - new Date(b.tanggal).getTime())
                })()}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="tanggal"
                  fontSize={12}
                />
                <YAxis />
                <ChartTooltip
                  content={<ChartTooltipContent />}
                  formatter={(value, name) => [
                    name === 'penjualan' ? `Rp ${value.toLocaleString()}` : `${value} transaksi`,
                    name === 'penjualan' ? 'Total Penjualan' : 'Jumlah Transaksi'
                  ]}
                />
                <Line
                  type="monotone"
                  dataKey="penjualan"
                  stroke="#f97316"
                  strokeWidth={3}
                  dot={{ fill: '#f97316', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ChartContainer>
          </CardContent>
        </Card>
        </div>
      </div>

      {/* Rata-rata Penjualan Section */}
      <div className="mt-8">
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-xl font-bold text-gray-900">📊 Rata-rata Penjualan per Produk</h2>
              <p className="text-sm text-gray-600">
                Analisis rata-rata penjualan harian untuk setiap produk per Kang Ider
              </p>
            </div>

            {/* Dropdown Pilih Kang Ider */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Filter Kang Ider:</span>
              <Select value={selectedAverageKangider} onValueChange={setSelectedAverageKangider}>
                <SelectTrigger className="w-[200px] h-9">
                  <SelectValue placeholder="Pilih Kang Ider..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Kang Ider</SelectItem>
                  {kangIders.map((kangider) => (
                    <SelectItem key={kangider.id} value={kangider.id}>
                      {kangider.nama}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
          {/* Grafik Rata-rata Penjualan per Produk */}
          <Card className="bg-white shadow-sm border-0 lg:col-span-2">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-purple-600" />
                Rata-rata Penjualan Harian (Semua Produk)
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                {selectedAverageKangider === "all"
                  ? "Semua Kang Ider"
                  : kangIders.find(k => k.id === selectedAverageKangider)?.nama || "Kang Ider Terpilih"
                } - Periode: {appliedDateRange.from && appliedDateRange.to
                  ? Math.ceil((appliedDateRange.to.getTime() - appliedDateRange.from.getTime()) / (1000 * 60 * 60 * 24)) + 1
                  : 1} hari
              </p>
            </CardHeader>
            <CardContent>
              <ChartContainer
                config={{
                  rata_rata: {
                    label: "Rata-rata Harian",
                    color: "hsl(var(--chart-3))",
                  },
                }}
                className="h-[500px]"
              >
                <BarChart
                  data={(() => {
                    // Filter penjualan berdasarkan Kang Ider yang dipilih
                    const filteredPenjualanByKangider = selectedAverageKangider === "all"
                      ? filteredPenjualan
                      : filteredPenjualan.filter(p => p.kangider?.id === selectedAverageKangider);

                    // Filter stock berdasarkan Kang Ider yang dipilih
                    const filteredStockByKangider = selectedAverageKangider === "all"
                      ? filteredStockHarian
                      : filteredStockHarian.filter(s => s.id_kangider === selectedAverageKangider);

                    // Group penjualan berdasarkan filter Kang Ider
                    const groupedPenjualanByKangider = filteredPenjualanByKangider.reduce((acc, item) => {
                      item.id_detail_penjualan.forEach((detail) => {
                        const idProduk = detail.idproduk.id
                        if (!acc[idProduk]) {
                          acc[idProduk] = {
                            nama_produk: detail.idproduk.nama_produk,
                            total_terjual: 0,
                            total_harga: 0,
                            harga_produk: detail.harga,
                          }
                        }
                        acc[idProduk].total_terjual += detail.jumlah
                        acc[idProduk].total_harga += detail.harga * detail.jumlah
                      })
                      return acc
                    }, {} as Record<string, { nama_produk: string; total_terjual: number; total_harga: number; harga_produk: number }>)

                    // Group stock berdasarkan filter Kang Ider
                    const groupedStockByKangider = filteredStockByKangider.reduce((acc, item) => {
                      const idProduk = item.id_produk.id
                      if (!acc[idProduk]) {
                        acc[idProduk] = {
                          nama_produk: item.id_produk.nama_produk,
                          stock_awal: 0,
                          stock_akhir: 0,
                          harga_produk: item.id_produk.harga,
                        }
                      }
                      acc[idProduk].stock_awal += item.stock_awal
                      acc[idProduk].stock_akhir += item.stock_akhir
                      return acc
                    }, {} as Record<string, { nama_produk: string; stock_awal: number; stock_akhir: number; harga_produk: number }>)

                    // Hitung jumlah hari dalam periode
                    const totalDays = appliedDateRange.from && appliedDateRange.to
                      ? Math.ceil((appliedDateRange.to.getTime() - appliedDateRange.from.getTime()) / (1000 * 60 * 60 * 24)) + 1
                      : 1;

                    // Gabungkan data stock dan penjualan untuk menghitung rata-rata
                    return Object.keys(groupedStockByKangider)
                      .map((idProduk) => {
                        const stockItem = groupedStockByKangider[idProduk]
                        const penjualanItem = groupedPenjualanByKangider[idProduk] || { total_terjual: 0 }
                        const rataRataHarian = penjualanItem.total_terjual / totalDays

                        return {
                          nama: stockItem.nama_produk,
                          rata_rata: Math.round(rataRataHarian * 100) / 100, // Round to 2 decimal places
                          total_terjual: penjualanItem.total_terjual,
                          periode_hari: totalDays
                        }
                      })
                      .filter(item => item.total_terjual > 0) // Only show products with sales
                      .sort((a, b) => b.rata_rata - a.rata_rata) // Sort by highest average first
                  })()}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="nama"
                    angle={-45}
                    textAnchor="end"
                    height={80}
                    fontSize={12}
                  />
                  <YAxis />
                  <ChartTooltip
                    content={<ChartTooltipContent />}
                    formatter={(value) => [
                      `${value} cup/hari`,
                      'Rata-rata Harian'
                    ]}
                    labelFormatter={(label) => `Produk: ${label}`}
                  />
                  <Bar dataKey="rata_rata" fill="#8b5cf6" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ChartContainer>
            </CardContent>
          </Card>

        {/* Detail Statistik per Kang Ider */}
        <Card className="bg-white shadow-sm border-0">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-indigo-600" />
                  Detail Statistik
                </CardTitle>
                <p className="text-sm text-gray-600">
                  {selectedAverageKangider === "all" ? "Semua Kang Ider" : kangIders.find(k => k.id === selectedAverageKangider)?.nama}
                </p>
              </div>

              {/* Toggle View Mode */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setStatisticViewMode("cup")}
                  className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                    statisticViewMode === "cup"
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-800"
                  }`}
                >
                  Cup
                </button>
                <button
                  onClick={() => setStatisticViewMode("value")}
                  className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                    statisticViewMode === "value"
                      ? "bg-white text-green-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-800"
                  }`}
                >
                  Nilai
                </button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {(() => {
              // Filter data berdasarkan Kang Ider yang dipilih
              const filteredPenjualanByKangider = selectedAverageKangider === "all"
                ? filteredPenjualan
                : filteredPenjualan.filter(p => p.kangider?.id === selectedAverageKangider);

              // Hitung statistik
              const totalDays = appliedDateRange.from && appliedDateRange.to
                ? Math.ceil((appliedDateRange.to.getTime() - appliedDateRange.from.getTime()) / (1000 * 60 * 60 * 24)) + 1
                : 1;

              const totalTransaksi = filteredPenjualanByKangider.length;
              const totalPenjualan = filteredPenjualanByKangider.reduce((sum, p) => sum + p.total_harga, 0);
              const rataRataTransaksiPerHari = Math.round((totalTransaksi / totalDays) * 100) / 100;
              const rataRataPenjualanPerHari = Math.round((totalPenjualan / totalDays) * 100) / 100;

              // Hitung jumlah produk unik yang terjual
              const produkUnik = new Set();
              filteredPenjualanByKangider.forEach(p => {
                p.id_detail_penjualan.forEach(detail => {
                  produkUnik.add(detail.idproduk.id);
                });
              });

              // Hitung total cup terjual
              const totalCupTerjual = filteredPenjualanByKangider.reduce((sum, p) => {
                return sum + p.id_detail_penjualan.reduce((detailSum, detail) => detailSum + detail.jumlah, 0);
              }, 0);

              const rataRataCupPerHari = Math.round((totalCupTerjual / totalDays) * 100) / 100;

              // Hitung detail per produk
              const produkDetail: Record<string, {
                nama: string;
                totalCup: number;
                totalNilai: number;
                rataRataHarian: number;
              }> = {};

              filteredPenjualanByKangider.forEach(p => {
                p.id_detail_penjualan.forEach(detail => {
                  const produkId = detail.idproduk.id;
                  const produkNama = detail.idproduk.nama_produk;

                  if (!produkDetail[produkId]) {
                    produkDetail[produkId] = {
                      nama: produkNama,
                      totalCup: 0,
                      totalNilai: 0,
                      rataRataHarian: 0
                    };
                  }

                  produkDetail[produkId].totalCup += detail.jumlah;
                  produkDetail[produkId].totalNilai += detail.harga * detail.jumlah;
                });
              });

              // Hitung rata-rata harian untuk setiap produk
              Object.keys(produkDetail).forEach(produkId => {
                produkDetail[produkId].rataRataHarian = Math.round((produkDetail[produkId].totalCup / totalDays) * 100) / 100;
              });

              // Sort produk berdasarkan total cup terjual
              const sortedProduk = Object.values(produkDetail)
                .sort((a, b) => b.totalCup - a.totalCup)
                .slice(0, 5); // Top 5 produk

              return (
                <div className="space-y-4">
                  {/* Ringkasan Utama */}
                  <div className="grid grid-cols-1 gap-3">
                    <div className="text-center p-3 bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg border border-purple-200">
                      <div className="text-xl font-bold text-purple-600">
                        {totalTransaksi}
                      </div>
                      <div className="text-sm text-gray-600">Total Transaksi</div>
                      <div className="text-xs text-purple-600 mt-1">
                        {rataRataTransaksiPerHari}/hari
                      </div>
                    </div>
                    <div className="text-center p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200">
                      <div className="text-xl font-bold text-green-600">
                        Rp {totalPenjualan.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-600">Total Penjualan</div>
                      <div className="text-xs text-green-600 mt-1">
                        Rp {rataRataPenjualanPerHari.toLocaleString()}/hari
                      </div>
                    </div>
                  </div>

                  {/* Top 5 Produk */}
                  <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <h4 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
                      🏆 Top 5 Produk Terlaris
                    </h4>
                    <div className="space-y-2">
                      {sortedProduk.length > 0 ? sortedProduk.map((produk, index) => (
                        <div key={index} className="flex justify-between items-center p-2 bg-white rounded border">
                          <div className="flex items-center gap-2">
                            <span className="text-xs font-bold text-blue-600 bg-blue-100 rounded-full w-5 h-5 flex items-center justify-center">
                              {index + 1}
                            </span>
                            <span className="text-sm font-medium text-gray-700 truncate max-w-[120px]" title={produk.nama}>
                              {produk.nama}
                            </span>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-bold text-blue-600">
                              {produk.totalCup} cup
                            </div>
                            <div className="text-xs text-gray-500">
                              {produk.rataRataHarian}/hari
                            </div>
                          </div>
                        </div>
                      )) : (
                        <div className="text-center text-gray-500 py-4">
                          Tidak ada data produk
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Ringkasan Produk & Cup */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="text-center p-3 bg-gradient-to-r from-indigo-50 to-indigo-100 rounded-lg border border-indigo-200">
                      <div className="text-lg font-bold text-indigo-600">
                        {produkUnik.size}
                      </div>
                      <div className="text-xs text-gray-600">Jenis Produk</div>
                    </div>
                    <div className="text-center p-3 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg border border-orange-200">
                      <div className="text-lg font-bold text-orange-600">
                        {totalCupTerjual}
                      </div>
                      <div className="text-xs text-gray-600">Total Cup</div>
                      <div className="text-xs text-orange-600 mt-1">
                        {rataRataCupPerHari}/hari
                      </div>
                    </div>
                  </div>

                  {/* Info Periode */}
                  <div className="text-center p-3 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-200">
                    <div className="text-sm font-semibold text-gray-700">
                      📅 {totalDays} hari
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {format(appliedDateRange.from!, "dd/MM")} - {format(appliedDateRange.to || appliedDateRange.from!, "dd/MM")}
                    </div>
                  </div>
                </div>
              );
            })()}
          </CardContent>
        </Card>
        </div>
      </div>

      {/* Pie Chart Section - Distribusi Penjualan per Produk */}
      <div className="mt-8">
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h2 className="text-xl font-bold text-gray-900">🥧 Distribusi Penjualan per Produk</h2>
              <p className="text-sm text-gray-600">
                Distribusi penjualan kangider untuk produk yang dipilih
              </p>
            </div>

            {/* Dropdown Pilih Produk */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Pilih Produk:</span>
              <Select value={selectedPieChartProduct} onValueChange={setSelectedPieChartProduct}>
                <SelectTrigger className="w-[200px] h-9">
                  <SelectValue placeholder="Pilih produk..." />
                </SelectTrigger>
                <SelectContent>
                  {availableProducts.map((product) => (
                    <SelectItem key={product.id} value={product.id}>
                      {product.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {!selectedPieChartProduct || availableProducts.length === 0 ? (
          <div className="p-8 text-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <PieChart className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {availableProducts.length === 0 ? 'Tidak Ada Data Penjualan' : 'Pilih Produk'}
            </h3>
            <p className="text-gray-600">
              {availableProducts.length === 0
                ? 'Tidak ada data penjualan untuk periode dan filter yang dipilih.'
                : 'Pilih produk dari dropdown di atas untuk melihat distribusi penjualan kangider.'
              }
            </p>
          </div>
        ) : (
          (() => {
            const selectedProductName = availableProducts.find(p => p.id === selectedPieChartProduct)?.name || 'Unknown'
            const kangiderData = pieChartData[selectedProductName] || {}
            const totalPenjualan = Object.values(kangiderData).reduce((sum, qty) => sum + qty, 0)

            if (totalPenjualan === 0) {
              return (
                <div className="p-8 text-center bg-yellow-50 rounded-lg border-2 border-dashed border-yellow-300">
                  <PieChart className="h-12 w-12 mx-auto mb-4 text-yellow-400" />
                  <h3 className="text-lg font-medium text-yellow-900 mb-2">Tidak Ada Penjualan</h3>
                  <p className="text-yellow-700">
                    Produk "{selectedProductName}" tidak memiliki penjualan pada periode yang dipilih.
                  </p>
                </div>
              )
            }

            const chartData = Object.entries(kangiderData).map(([kangiderName, quantity]) => ({
              name: kangiderName,
              value: quantity as number,
              percentage: (((quantity as number) / totalPenjualan) * 100).toFixed(1)
            })).sort((a, b) => b.value - a.value)

            // Warna untuk setiap slice
            const COLORS = ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4', '#84cc16', '#f97316']

            return (
              <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
                {/* Pie Chart */}
                <Card className="bg-white shadow-sm border-0 lg:col-span-2">
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                      <PieChart className="h-5 w-5 text-blue-600" />
                      {selectedProductName}
                    </CardTitle>
                    <p className="text-sm text-gray-600">
                      Total: {totalPenjualan} cup terjual | {Object.keys(kangiderData).length} kangider aktif
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="h-[400px]">
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsPC>
                          <Pie
                            data={chartData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            label={({ name, percentage }) => `${name}: ${percentage}%`}
                            outerRadius={120}
                            fill="#8884d8"
                            dataKey="value"
                          >
                            {chartData.map((_, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <ChartTooltip
                            content={({ active, payload }) => {
                              if (active && payload && payload.length) {
                                const data = payload[0].payload
                                return (
                                  <div className="bg-white p-3 border rounded-lg shadow-lg">
                                    <p className="font-medium">Kangider: {data.name}</p>
                                    <p className="text-sm text-gray-600">
                                      Penjualan: {data.value} cup ({data.percentage}%)
                                    </p>
                                    <p className="text-xs text-gray-500">
                                      Produk: {selectedProductName}
                                    </p>
                                  </div>
                                )
                              }
                              return null
                            }}
                          />
                          <Legend />
                        </RechartsPC>
                      </ResponsiveContainer>
                    </div>
                  </CardContent>
                </Card>

                {/* Detail Statistics */}
                <Card className="bg-white shadow-sm border-0">
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-gray-900">📊 Detail Statistik</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {chartData.map((item, index) => (
                        <div key={item.name} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div
                              className="w-4 h-4 rounded-full"
                              style={{ backgroundColor: COLORS[index % COLORS.length] }}
                            ></div>
                            <div>
                              <p className="font-medium text-gray-900">{item.name}</p>
                              <p className="text-sm text-gray-600">{item.percentage}% dari total</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-semibold text-gray-900">{item.value}</p>
                            <p className="text-xs text-gray-500">cup</p>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Summary */}
                    <div className="mt-6 pt-4 border-t border-gray-200">
                      <div className="grid grid-cols-2 gap-4 text-center">
                        <div>
                          <p className="text-2xl font-bold text-blue-600">{totalPenjualan}</p>
                          <p className="text-sm text-gray-600">Total Cup</p>
                        </div>
                        <div>
                          <p className="text-2xl font-bold text-green-600">{Object.keys(kangiderData).length}</p>
                          <p className="text-sm text-gray-600">Kangider Aktif</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )
          })()
        )}
      </div>

      {/* Detail Section */}
      <div className="mt-8">
        <div className="mb-4">
          <h2 className="text-xl font-bold text-gray-900">📋 Detail Data</h2>
          <p className="text-sm text-gray-600">Informasi lengkap menu, pengeluaran, dan lokasi</p>
        </div>
        <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
        <Card className="bg-white shadow-sm border-0 lg:col-span-2">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900">Daftar Menu dan Penjualan</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="font-semibold text-gray-700">Menu</TableHead>
                    <TableHead className="font-semibold text-gray-700">Harga</TableHead>
                    <TableHead className="font-semibold text-gray-700">Stock Awal</TableHead>
                    <TableHead className="font-semibold text-gray-700">Terjual</TableHead>
                    <TableHead className="font-semibold text-gray-700">Stock Akhir</TableHead>
                    <TableHead className="font-semibold text-gray-700">Nilai Penjualan</TableHead>
                    <TableHead className="font-semibold text-gray-700">Status Stock</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Object.keys(groupedData.groupedStock).length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                        Tidak ada data menu untuk ditampilkan
                      </TableCell>
                    </TableRow>
                  ) : (
                    Object.keys(groupedData.groupedStock).map((idProduk) => {
                      const stockItem = groupedData.groupedStock[idProduk]
                      const penjualanItem = groupedData.groupedPenjualan[idProduk] || { total_terjual: 0, total_harga: 0, harga_produk: 0 }

                      // Gunakan harga dari stock item atau penjualan item
                      const hargaProduk = stockItem.harga_produk || penjualanItem.harga_produk || 0;

                      // Hitung nilai penjualan (harga × jumlah terjual)
                      const nilaiPenjualan = hargaProduk * penjualanItem.total_terjual;

                      // Hitung persentase stock terjual
                      const stockTerjualPersen = stockItem.stock_awal > 0 ? (penjualanItem.total_terjual / stockItem.stock_awal) * 100 : 0;

                      // Status stock berdasarkan sisa stock
                      const stockStatus = stockItem.stock_akhir === 0 ? 'Habis' :
                                         stockItem.stock_akhir <= stockItem.stock_awal * 0.2 ? 'Sedikit' : 'Cukup';

                      const statusColor = stockStatus === 'Habis' ? 'text-red-600 bg-red-50' :
                                         stockStatus === 'Sedikit' ? 'text-yellow-600 bg-yellow-50' : 'text-green-600 bg-green-50';

                      return (
                        <TableRow key={idProduk} className="hover:bg-gray-50">
                          <TableCell className="font-medium text-gray-900">{stockItem.nama_produk}</TableCell>
                          <TableCell className="text-gray-700">Rp {hargaProduk.toLocaleString()}</TableCell>
                          <TableCell className="text-center">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700">
                              {stockItem.stock_awal}
                            </span>
                          </TableCell>
                          <TableCell className="text-center">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-50 text-purple-700">
                              {penjualanItem.total_terjual}
                            </span>
                          </TableCell>
                          <TableCell className="text-center">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-50 text-gray-700">
                              {stockItem.stock_akhir}
                            </span>
                          </TableCell>
                          <TableCell className="font-medium text-gray-900">Rp {nilaiPenjualan.toLocaleString()}</TableCell>
                          <TableCell>
                            <div className="flex flex-col space-y-1">
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusColor}`}>
                                {stockStatus}
                              </span>
                              <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full ${stockStatus === 'Habis' ? 'bg-red-500' : stockStatus === 'Sedikit' ? 'bg-yellow-500' : 'bg-green-500'}`}
                                  style={{ width: `${Math.min(stockTerjualPersen, 100)}%` }}
                                ></div>
                              </div>
                              <span className="text-xs text-gray-500">{stockTerjualPersen.toFixed(0)}% terjual</span>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Summary Section */}
            {Object.keys(groupedData.groupedStock).length > 0 && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {Object.values(groupedData.groupedStock).reduce((sum, item) => sum + item.stock_awal, 0)}
                    </div>
                    <div className="text-sm text-gray-600">Total Stock Awal</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {Object.values(groupedData.groupedPenjualan).reduce((sum, item) => sum + item.total_terjual, 0)}
                    </div>
                    <div className="text-sm text-gray-600">Total Cup Terjual</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600">
                      {Object.values(groupedData.groupedStock).reduce((sum, item) => sum + item.stock_akhir, 0)}
                    </div>
                    <div className="text-sm text-gray-600">Total Stock Akhir</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      Rp {Object.keys(groupedData.groupedStock).reduce((sum, idProduk) => {
                        const stockItem = groupedData.groupedStock[idProduk]
                        const penjualanItem = groupedData.groupedPenjualan[idProduk] || { total_terjual: 0 }
                        const hargaProduk = stockItem.harga_produk || 0
                        return sum + (hargaProduk * penjualanItem.total_terjual)
                      }, 0).toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">Total Nilai Penjualan</div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="space-y-6">
          <Card className="bg-white shadow-sm border-0">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900">Detail Pengeluaran</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Operational Expenses */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Pengeluaran Operasional</h4>
                  {filteredOperationalExpenses.length === 0 ? (
                    <p className="text-sm text-gray-500">Tidak ada pengeluaran operasional</p>
                  ) : (
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {filteredOperationalExpenses.map((expense) => (
                        <div key={expense.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                          <div>
                            <p className="text-sm font-medium">{expense.expense_name}</p>
                            <p className="text-xs text-gray-500">{expense.keterangan}</p>
                            <p className="text-xs text-gray-400">{expense.expense_date} {expense.waktu}</p>
                          </div>
                          <span className="text-sm font-medium text-red-600">
                            -Rp {expense.amount.toLocaleString()}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                  <div className="mt-2 pt-2 border-t">
                    <div className="flex justify-between text-sm font-medium">
                      <span>Subtotal Operasional:</span>
                      <span className="text-red-600">Rp {totals.totalOperationalExpenses.toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                {/* Internal Usage */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Penggunaan Internal</h4>
                  {filteredInternalUsages.length === 0 ? (
                    <p className="text-sm text-gray-500">Tidak ada penggunaan internal</p>
                  ) : (
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {filteredInternalUsages.map((usage) => (
                        <div key={usage.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                          <div>
                            <p className="text-sm font-medium">{getProductName(usage)}</p>
                            <p className="text-xs text-gray-500">Qty: {usage.quantity} × Rp {parseInt(usage.unit_price).toLocaleString()}</p>
                            <p className="text-xs text-gray-400">
                              {usage.usage_date && usage.usage_date !== "0"
                                ? `${usage.usage_date} ${usage.waktu}`
                                : usage.date_created
                                  ? `${format(new Date(usage.date_created), "yyyy-MM-dd")} ${usage.waktu}`
                                  : `Tanggal tidak tersedia ${usage.waktu}`
                              }
                            </p>
                          </div>
                          <span className="text-sm font-medium text-red-600">
                            -Rp {usage.total_amount.toLocaleString()}
                          </span>
                        </div>
                      ))}
                    </div>
                  )}
                  <div className="mt-2 pt-2 border-t">
                    <div className="flex justify-between text-sm font-medium">
                      <span>Subtotal Internal:</span>
                      <span className="text-red-600">Rp {totals.totalInternalUsage.toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                {/* Total */}
                <div className="pt-2 border-t-2 border-gray-300">
                  <div className="flex justify-between text-base font-bold">
                    <span>Total Pengeluaran:</span>
                    <span className="text-red-600">Rp {totals.totalPengeluaran.toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white shadow-sm border-0">
            <CardHeader>
              <CardTitle className="text-lg font-semibold text-gray-900">Lokasi Berjualan</CardTitle>
            </CardHeader>
            <CardContent>
              <Map locations={lokasiKangider} />
            </CardContent>
          </Card>
        </div>
        </div>
      </div>
    </div>
  )
}
